import React, { useState, useEffect } from 'react';
import {
  BarChart3,
  Users,
  MessageSquare,
  DollarSign,
  Clock,
  XCircle,
  Eye,
  Trash2,
  LogOut,
  Calendar,
  Mail,
  Phone,
  RefreshCw,
  Menu,
  Activity,
  Settings
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { firebaseService } from '../services/firebase';
import LoadingSpinner from '../components/LoadingSpinner';
import UserAvatar from '../components/UserAvatar';
import RealTimeAnalyticsDashboard from '../components/RealTimeAnalyticsDashboard';
import PricingManagement from '../components/PricingManagement';
import { formatDisplayDate, formatSimpleDate } from '../utils/dateUtils';

interface Quote {
  id: string;
  name: string;
  email: string;
  phone: string;
  serviceType: string;
  propertySize: string;
  frequency: string;
  additionalServices: string[];
  message: string;
  status: string;
  pricing?: {
    total: number;
    subtotal: number;
    discountAmount: number;
  };
  timestamp: string;
}

interface Contact {
  id: string;
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
  status: string;
  timestamp: string;
}

interface Analytics {
  totalQuotes: number;
  totalContacts: number;
  pendingQuotes: number;
  newContacts: number;
  totalRevenue: number;
}

const AdminPage: React.FC = () => {
  const { user, isAdmin, signOut } = useAuth();
  const [activeTab, setActiveTab] = useState<'dashboard' | 'analytics' | 'quotes' | 'contacts' | 'pricing'>('dashboard');
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState<Quote | Contact | null>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    if (isAdmin) {
      loadData();
    }
  }, [isAdmin]);

  const loadData = async () => {
    setLoading(true);
    try {
      const [quotesData, contactsData, analyticsData] = await Promise.all([
        firebaseService.admin.getQuotes(),
        firebaseService.admin.getContacts(),
        firebaseService.admin.getAnalytics()
      ]);
      
      setQuotes(quotesData);
      setContacts(contactsData);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Error loading admin data:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateQuoteStatus = async (quoteId: string, status: string) => {
    try {
      await firebaseService.admin.updateQuoteStatus(quoteId, status);
      await loadData(); // Refresh data
    } catch (error) {
      console.error('Error updating quote status:', error);
    }
  };

  const updateContactStatus = async (contactId: string, status: string) => {
    try {
      await firebaseService.admin.updateContactStatus(contactId, status);
      await loadData(); // Refresh data
    } catch (error) {
      console.error('Error updating contact status:', error);
    }
  };

  const deleteQuote = async (quoteId: string) => {
    if (window.confirm('Are you sure you want to delete this quote?')) {
      try {
        await firebaseService.admin.deleteQuote(quoteId);
        await loadData(); // Refresh data
      } catch (error) {
        console.error('Error deleting quote:', error);
      }
    }
  };

  const deleteContact = async (contactId: string) => {
    if (window.confirm('Are you sure you want to delete this contact?')) {
      try {
        await firebaseService.admin.deleteContact(contactId);
        await loadData(); // Refresh data
      } catch (error) {
        console.error('Error deleting contact:', error);
      }
    }
  };

  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <LoadingSpinner size="large" text="Loading admin panel..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <header className="lg:hidden bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="px-4 sm:px-6">
          <div className="flex justify-between items-center h-16">
            {/* Logo & Title */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <div className="bg-gray-50 rounded-lg p-1.5 border border-gray-200">
                  <img 
                    src="/logo.png" 
                    alt="Ottawa Shine Solutions" 
                    className="h-8 w-8 object-contain"
                  />
                </div>
                <div className="hidden sm:block">
                  <h1 className="text-lg font-bold text-gray-900">Ottawa Shine Solutions</h1>
                  <p className="text-xs text-gray-500">Admin Dashboard</p>
                </div>
                <div className="sm:hidden">
                  <h1 className="text-base font-bold text-gray-900">Admin</h1>
                </div>
              </div>
            </div>

            {/* Mobile Actions */}
            <div className="flex items-center space-x-2">
              <button
                onClick={loadData}
                disabled={loading}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                title="Refresh Data"
              >
                <RefreshCw className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
              </button>
              
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Menu className="h-5 w-5" />
              </button>
              
              <button
                onClick={signOut}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                title="Sign Out"
              >
                <LogOut className="h-5 w-5" />
              </button>
            </div>
          </div>
          
          {/* Mobile Navigation Menu */}
          {mobileMenuOpen && (
            <div className="border-t border-gray-200 py-3">
              <div className="flex flex-col space-y-1">
                {[
                  { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
                  { id: 'analytics', label: 'Live Analytics', icon: Activity },
                  { id: 'quotes', label: 'Quote Requests', icon: Users },
                  { id: 'contacts', label: 'Messages', icon: MessageSquare },
                  { id: 'pricing', label: 'Pricing', icon: Settings }
                ].map(({ id, label, icon: Icon }) => (
                  <button
                    key={id}
                    onClick={() => {
                      setActiveTab(id as 'dashboard' | 'analytics' | 'quotes' | 'contacts' | 'pricing');
                      setMobileMenuOpen(false);
                    }}
                    className={`flex items-center space-x-3 px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                      activeTab === id
                        ? 'bg-green-50 text-green-600 border border-green-200'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{label}</span>
                  </button>
                ))}
                
                {/* Mobile User Info */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-3 px-4 py-2">
                    <UserAvatar user={user} size="sm" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{user?.displayName || 'Admin'}</p>
                      <p className="text-xs text-gray-500">{user?.email}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Desktop Layout */}
      <div className="hidden lg:flex h-screen">
        {/* Desktop Sidebar */}
        <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
          {/* Sidebar Header */}
          <div className="flex items-center px-6 py-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="bg-gray-50 rounded-lg p-2 border border-gray-200">
                <img 
                  src="/logo.png" 
                  alt="Ottawa Shine Solutions" 
                  className="h-8 w-8 object-contain"
                />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900">Ottawa Shine</h1>
                <p className="text-xs text-gray-500">Admin Dashboard</p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {[
              { id: 'dashboard', label: 'Dashboard', icon: BarChart3, description: 'Overview & stats' },
              { id: 'analytics', label: 'Live Analytics', icon: Activity, description: 'Real-time data' },
              { id: 'quotes', label: 'Quote Requests', icon: Users, description: 'Manage quotes' },
              { id: 'contacts', label: 'Messages', icon: MessageSquare, description: 'Customer messages' },
              { id: 'pricing', label: 'Pricing', icon: Settings, description: 'Manage service prices' }
            ].map(({ id, label, icon: Icon, description }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as 'dashboard' | 'analytics' | 'quotes' | 'contacts' | 'pricing')}
                className={`w-full flex items-start space-x-3 px-3 py-3 text-left rounded-xl transition-all duration-200 group ${
                  activeTab === id
                    ? 'bg-green-50 text-green-700 border border-green-200 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <div className={`p-2 rounded-lg ${
                  activeTab === id 
                    ? 'bg-green-100 text-green-600' 
                    : 'bg-gray-100 text-gray-500 group-hover:bg-gray-200'
                }`}>
                  <Icon className="h-5 w-5" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium">{label}</p>
                  <p className="text-xs text-gray-500 mt-0.5">{description}</p>
                </div>
              </button>
            ))}
          </nav>

          {/* User Profile */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex items-center space-x-3 mb-3">
              <UserAvatar user={user} size="md" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900 truncate">{user?.displayName || 'Admin'}</p>
                <p className="text-xs text-gray-500 truncate">{user?.email}</p>
              </div>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={loadData}
                disabled={loading}
                className="flex-1 flex items-center justify-center px-3 py-2 text-xs font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                title="Refresh Data"
              >
                <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
              <button
                onClick={signOut}
                className="flex-1 flex items-center justify-center px-3 py-2 text-xs font-medium text-red-600 bg-red-50 hover:bg-red-100 rounded-lg transition-colors"
                title="Sign Out"
              >
                <LogOut className="h-4 w-4 mr-1" />
                Sign Out
              </button>
            </div>
          </div>
        </div>

        {/* Desktop Main Content */}
        <div className="flex-1 overflow-auto">
          <main className="p-8">
            {/* Desktop Content */}
            {activeTab === 'dashboard' && analytics && (
              <div className="space-y-8">
                {/* Welcome Section */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome back, {user?.displayName?.split(' ')[0] || 'Admin'}! 👋</h2>
                      <p className="text-gray-600">Here's what's happening with your business today.</p>
                    </div>
                    <BarChart3 className="h-12 w-12 text-green-600" />
                  </div>
                </div>
                
                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Quote Requests</p>
                        <p className="text-3xl font-bold text-gray-900 mt-2">{analytics.totalQuotes}</p>
                        <p className="text-sm text-gray-600 mt-1">Total received</p>
                      </div>
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <Users className="h-8 w-8 text-blue-600" />
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Messages</p>
                        <p className="text-3xl font-bold text-gray-900 mt-2">{analytics.totalContacts}</p>
                        <p className="text-sm text-gray-600 mt-1">Customer inquiries</p>
                      </div>
                      <div className="bg-green-50 p-3 rounded-lg">
                        <MessageSquare className="h-8 w-8 text-green-600" />
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Pending</p>
                        <p className="text-3xl font-bold text-gray-900 mt-2">{analytics.pendingQuotes}</p>
                        <p className="text-sm text-gray-600 mt-1">Awaiting response</p>
                      </div>
                      <div className="bg-yellow-50 p-3 rounded-lg">
                        <Clock className="h-8 w-8 text-yellow-600" />
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Revenue</p>
                        <p className="text-3xl font-bold text-gray-900 mt-2">${analytics.totalRevenue.toLocaleString()}</p>
                        <p className="text-sm text-gray-600 mt-1">Total earnings</p>
                      </div>
                      <div className="bg-purple-50 p-3 rounded-lg">
                        <DollarSign className="h-8 w-8 text-purple-600" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Recent Activity Cards */}
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                  {/* Recent Quotes */}
                  <div className="bg-white rounded-xl border border-gray-200">
                    <div className="p-6 border-b border-gray-100">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-gray-900">Recent Quote Requests</h3>
                        <button 
                          onClick={() => setActiveTab('quotes')}
                          className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                        >
                          View all →
                        </button>
                      </div>
                    </div>
                    <div className="p-6 space-y-4">
                      {quotes.slice(0, 5).map((quote) => (
                        <div key={quote.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                          <div className="flex items-center space-x-4">
                            <div className="bg-blue-100 p-2 rounded-full">
                              <Users className="h-4 w-4 text-blue-600" />
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">{quote.name}</p>
                              <p className="text-sm text-gray-500">{quote.serviceType}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                              quote.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              quote.status === 'approved' ? 'bg-green-100 text-green-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {quote.status}
                            </span>
                            <p className="text-sm text-gray-500 mt-1">
                              {quote.pricing ? `$${quote.pricing.total}` : 'TBD'}
                            </p>
                          </div>
                        </div>
                      ))}
                      {quotes.length === 0 && (
                        <div className="text-center py-8">
                          <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <p className="text-gray-500">No quote requests yet</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Recent Messages */}
                  <div className="bg-white rounded-xl border border-gray-200">
                    <div className="p-6 border-b border-gray-100">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-gray-900">Recent Messages</h3>
                        <button 
                          onClick={() => setActiveTab('contacts')}
                          className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                        >
                          View all →
                        </button>
                      </div>
                    </div>
                    <div className="p-6 space-y-4">
                      {contacts.slice(0, 5).map((contact) => (
                        <div key={contact.id} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                          <div className="bg-green-100 p-2 rounded-full">
                            <MessageSquare className="h-4 w-4 text-green-600" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="font-medium text-gray-900 truncate">{contact.name}</p>
                              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                                contact.status === 'new' ? 'bg-green-100 text-green-800' :
                                contact.status === 'read' ? 'bg-blue-100 text-blue-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {contact.status}
                              </span>
                            </div>
                            <p className="text-sm font-medium text-gray-600 truncate">{contact.subject}</p>
                            <p className="text-sm text-gray-500 truncate">{contact.message}</p>
                          </div>
                        </div>
                      ))}
                      {contacts.length === 0 && (
                        <div className="text-center py-8">
                          <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <p className="text-gray-500">No messages yet</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'analytics' && (
              <div>
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">Real-Time Analytics</h2>
                  <p className="text-gray-600 mt-1">Monitor live user activity across your website</p>
                </div>
                <RealTimeAnalyticsDashboard />
              </div>
            )}

            {activeTab === 'quotes' && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Quote Management</h2>
                
                <div className="bg-white rounded-lg shadow overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Customer
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Service
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {quotes.length > 0 ? (
                        quotes.map((quote) => (
                          <tr key={quote.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900">{quote.name}</div>
                                <div className="text-sm text-gray-500">{quote.email}</div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">{quote.serviceType}</div>
                              <div className="text-sm text-gray-500">{quote.propertySize} • {quote.frequency}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {quote.pricing ? `$${quote.pricing.total}` : 'TBD'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <select
                                value={quote.status}
                                onChange={(e) => updateQuoteStatus(quote.id, e.target.value)}
                                className="text-sm border border-gray-300 rounded px-2 py-1"
                              >
                                <option value="pending">Pending</option>
                                <option value="approved">Approved</option>
                                <option value="rejected">Rejected</option>
                              </select>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => setSelectedItem(quote)}
                                  className="text-green-600 hover:text-green-900"
                                >
                                  <Eye className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => deleteQuote(quote.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={5} className="px-6 py-12 text-center">
                            <div className="flex flex-col items-center">
                              <Users className="h-12 w-12 text-gray-300 mb-4" />
                              <h3 className="text-lg font-medium text-gray-900 mb-2">No Quote Requests</h3>
                              <p className="text-gray-500">When customers submit quote requests, they will appear here.</p>
                            </div>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {activeTab === 'contacts' && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Message Management</h2>
                
                <div className="bg-white rounded-lg shadow overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Customer
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Subject
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {contacts.length > 0 ? (
                        contacts.map((contact) => (
                          <tr key={contact.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900">{contact.name}</div>
                                <div className="text-sm text-gray-500">{contact.email}</div>
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              <div className="text-sm text-gray-900">{contact.subject}</div>
                              <div className="text-sm text-gray-500 truncate max-w-xs">{contact.message}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatSimpleDate(contact.timestamp)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <select
                                value={contact.status}
                                onChange={(e) => updateContactStatus(contact.id, e.target.value)}
                                className="text-sm border border-gray-300 rounded px-2 py-1"
                              >
                                <option value="new">New</option>
                                <option value="responded">Responded</option>
                                <option value="closed">Closed</option>
                              </select>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => setSelectedItem(contact)}
                                  className="text-green-600 hover:text-green-900"
                                >
                                  <Eye className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => deleteContact(contact.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={5} className="px-6 py-12 text-center">
                            <div className="flex flex-col items-center">
                              <MessageSquare className="h-12 w-12 text-gray-300 mb-4" />
                              <h3 className="text-lg font-medium text-gray-900 mb-2">No Messages</h3>
                              <p className="text-gray-500">When customers send messages, they will appear here.</p>
                            </div>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {activeTab === 'pricing' && (
              <PricingManagement onUpdate={loadData} />
            )}
          </main>
        </div>
      </div>

      {/* Mobile Content */}
      <main className="lg:hidden px-4 sm:px-6 py-8">
        {activeTab === 'dashboard' && analytics && (
          <div className="space-y-8">
            {/* Welcome Section */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome back, {user?.displayName?.split(' ')[0] || 'Admin'}! 👋</h2>
                  <p className="text-gray-600">Here's what's happening with your business today.</p>
                </div>
                <BarChart3 className="h-12 w-12 text-green-600" />
              </div>
            </div>
            
            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white rounded-xl border border-gray-200 p-4 hover:shadow-lg transition-shadow">
                <div className="text-center">
                  <div className="bg-blue-50 p-3 rounded-lg inline-flex mb-3">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Quotes</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{analytics.totalQuotes}</p>
                </div>
              </div>
              
              <div className="bg-white rounded-xl border border-gray-200 p-4 hover:shadow-lg transition-shadow">
                <div className="text-center">
                  <div className="bg-green-50 p-3 rounded-lg inline-flex mb-3">
                    <MessageSquare className="h-6 w-6 text-green-600" />
                  </div>
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Messages</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{analytics.totalContacts}</p>
                </div>
              </div>
              
              <div className="bg-white rounded-xl border border-gray-200 p-4 hover:shadow-lg transition-shadow">
                <div className="text-center">
                  <div className="bg-yellow-50 p-3 rounded-lg inline-flex mb-3">
                    <Clock className="h-6 w-6 text-yellow-600" />
                  </div>
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Pending</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{analytics.pendingQuotes}</p>
                </div>
              </div>
              
              <div className="bg-white rounded-xl border border-gray-200 p-4 hover:shadow-lg transition-shadow">
                <div className="text-center">
                  <div className="bg-purple-50 p-3 rounded-lg inline-flex mb-3">
                    <DollarSign className="h-6 w-6 text-purple-600" />
                  </div>
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Revenue</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">${analytics.totalRevenue.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div>
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Real-Time Analytics</h2>
              <p className="text-gray-600 mt-1">Monitor live user activity across your website</p>
            </div>
            <RealTimeAnalyticsDashboard />
          </div>
        )}

        {activeTab === 'quotes' && (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Quote Management</h2>
            
            {/* Mobile Card View */}
            <div className="space-y-4">
              {quotes.length > 0 ? (
                quotes.map((quote) => (
                  <div key={quote.id} className="bg-white rounded-lg shadow p-4 space-y-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Customer</span>
                        <p className="text-sm font-medium text-gray-900">{quote.name}</p>
                        <p className="text-xs text-gray-500">{quote.email}</p>
                      </div>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        quote.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        quote.status === 'approved' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {quote.status}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Service</span>
                        <p className="text-sm text-gray-900">{quote.serviceType}</p>
                      </div>
                      <div>
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Total</span>
                        <p className="text-sm font-medium text-gray-900">
                          {quote.pricing ? `$${quote.pricing.total}` : 'TBD'}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <select
                        value={quote.status}
                        onChange={(e) => updateQuoteStatus(quote.id, e.target.value)}
                        className="text-sm border border-gray-300 rounded px-2 py-1 flex-1 mr-3"
                      >
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                      </select>
                      
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setSelectedItem(quote)}
                          className="p-2 text-green-600 hover:text-green-900 hover:bg-green-50 rounded"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => deleteQuote(quote.id)}
                          className="p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="bg-white rounded-lg shadow p-8 text-center">
                  <Users className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Quote Requests</h3>
                  <p className="text-gray-500">When customers submit quote requests, they will appear here.</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'contacts' && (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Message Management</h2>
            
            {/* Mobile Card View */}
            <div className="space-y-4">
              {contacts.length > 0 ? (
                contacts.map((contact) => (
                  <div key={contact.id} className="bg-white rounded-lg shadow p-4 space-y-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Customer</span>
                        <p className="text-sm font-medium text-gray-900">{contact.name}</p>
                        <p className="text-xs text-gray-500">{contact.email}</p>
                      </div>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        contact.status === 'new' ? 'bg-green-100 text-green-800' :
                        contact.status === 'responded' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {contact.status}
                      </span>
                    </div>
                    
                    <div className="space-y-2">
                      <div>
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Subject</span>
                        <p className="text-sm text-gray-900">{contact.subject}</p>
                      </div>
                      <div>
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Message</span>
                        <p className="text-sm text-gray-700 truncate">{contact.message}</p>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <select
                        value={contact.status}
                        onChange={(e) => updateContactStatus(contact.id, e.target.value)}
                        className="text-sm border border-gray-300 rounded px-2 py-1 flex-1 mr-3"
                      >
                        <option value="new">New</option>
                        <option value="responded">Responded</option>
                        <option value="closed">Closed</option>
                      </select>
                      
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setSelectedItem(contact)}
                          className="p-2 text-green-600 hover:text-green-900 hover:bg-green-50 rounded"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => deleteContact(contact.id)}
                          className="p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="bg-white rounded-lg shadow p-8 text-center">
                  <MessageSquare className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Messages</h3>
                  <p className="text-gray-500">When customers send messages, they will appear here.</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'pricing' && (
          <PricingManagement onUpdate={loadData} />
        )}
      </main>

      {/* Detail Modal */}
      {selectedItem && (
        <div 
          className="fixed inset-0 bg-gray-900 bg-opacity-20 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedItem(null)}
        >
          <div 
            className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-xl font-semibold text-gray-900">
                {'serviceType' in selectedItem ? '📋 Quote Request Details' : '💬 Message Details'}
              </h3>
              <button
                onClick={() => setSelectedItem(null)}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-200 rounded-lg transition-colors"
                title="Close"
              >
                <XCircle className="h-6 w-6" />
              </button>
            </div>
            
            {/* Modal Body */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Customer Information */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Customer Information</h4>
                  
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="bg-blue-100 p-2 rounded-lg">
                        <Users className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Full Name</label>
                        <p className="text-sm font-medium text-gray-900">{selectedItem.name}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="bg-green-100 p-2 rounded-lg">
                        <Mail className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Email Address</label>
                        <p className="text-sm text-gray-900">{selectedItem.email}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="bg-purple-100 p-2 rounded-lg">
                        <Phone className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Phone Number</label>
                        <p className="text-sm text-gray-900">{'phone' in selectedItem ? selectedItem.phone : 'N/A'}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="bg-yellow-100 p-2 rounded-lg">
                        <Calendar className="h-5 w-5 text-yellow-600" />
                      </div>
                      <div>
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Submitted</label>
                        <p className="text-sm text-gray-900">
                          {formatDisplayDate(selectedItem.timestamp)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Service/Message Details */}
                <div className="space-y-4">
                  {'serviceType' in selectedItem ? (
                    <>
                      <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Service Details</h4>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Service Type</label>
                          <p className="text-sm font-medium text-gray-900 bg-green-50 px-3 py-2 rounded-lg mt-1">{selectedItem.serviceType}</p>
                        </div>
                        
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Property Size</label>
                          <p className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg mt-1">{selectedItem.propertySize}</p>
                        </div>
                        
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Frequency</label>
                          <p className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg mt-1">{selectedItem.frequency}</p>
                        </div>

                        {selectedItem.additionalServices && selectedItem.additionalServices.length > 0 && (
                          <div>
                            <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Additional Services</label>
                            <div className="mt-1 flex flex-wrap gap-2">
                              {selectedItem.additionalServices.map((service, index) => (
                                <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  {service}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}

                        {selectedItem.message && selectedItem.message.trim() && (
                          <div>
                            <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Additional Message</label>
                            <div className="mt-1 bg-gray-50 px-3 py-2 rounded-lg">
                              <p className="text-sm text-gray-900 whitespace-pre-wrap">{selectedItem.message}</p>
                            </div>
                          </div>
                        )}

                        {selectedItem.pricing && (
                          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                            <label className="text-xs font-medium text-green-700 uppercase tracking-wide">Pricing Breakdown</label>
                            <div className="mt-2 space-y-1 text-sm">
                              <div className="flex justify-between">
                                <span>Subtotal:</span>
                                <span>${selectedItem.pricing.subtotal}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Discount:</span>
                                <span>-${selectedItem.pricing.discountAmount}</span>
                              </div>
                              <div className="flex justify-between font-medium text-green-800 border-t border-green-200 pt-1">
                                <span>Total:</span>
                                <span>${selectedItem.pricing.total}</span>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </>
                  ) : (
                    <>
                      <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Message Details</h4>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Subject</label>
                          <p className="text-sm font-medium text-gray-900 bg-blue-50 px-3 py-2 rounded-lg mt-1">{selectedItem.subject}</p>
                        </div>
                        
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Message</label>
                          <div className="bg-gray-50 p-4 rounded-lg border mt-1">
                            <p className="text-sm text-gray-900 whitespace-pre-wrap leading-relaxed">{selectedItem.message}</p>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                  
                  {/* Status */}
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Current Status</label>
                    <div className="mt-1">
                      <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${
                        selectedItem.status === 'pending' || selectedItem.status === 'new' 
                          ? 'bg-yellow-100 text-yellow-800' :
                        selectedItem.status === 'approved' || selectedItem.status === 'read'
                          ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {selectedItem.status}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="bg-gray-50 px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={() => setSelectedItem(null)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminPage;
