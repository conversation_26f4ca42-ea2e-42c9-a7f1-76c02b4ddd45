import React, { Suspense, lazy, useState, useEffect } from 'react';
import { Leaf } from 'lucide-react';
import LoadingSpinner from '../components/LoadingSpinner';
import { serviceConfig, getDynamicServiceConfig } from '../utils/services';
import { usePricing } from '../hooks/usePricing';
import type { Service } from '../types';

// Lazy load ServiceCard component
const ServiceCard = lazy(() => import('../components/ServiceCard'));

const ServicesPage: React.FC = () => {
  const [services, setServices] = useState<Service[]>(serviceConfig);
  const { pricingConfig, loading: pricingLoading } = usePricing();

  useEffect(() => {
    const loadDynamicServices = async () => {
      try {
        const dynamicServices = await getDynamicServiceConfig();
        setServices(dynamicServices);
      } catch (error) {
        console.error('Error loading dynamic services:', error);
        // Keep using static configuration as fallback
      }
    };

    if (pricingConfig && !pricingLoading) {
      loadDynamicServices();
    }
  }, [pricingConfig, pricingLoading]);

  return (
    <div className="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Our Services</h1>
          <div className="flex items-center justify-center mb-6">
            <Leaf className="h-6 w-6 text-green-600 mr-2" />
            <p className="text-lg text-gray-600">All services use 100% eco-friendly, non-toxic cleaning products</p>
          </div>
        </div>

        <div className="grid lg:grid-cols-1 gap-8 max-w-4xl mx-auto">
          <Suspense fallback={<LoadingSpinner size="large" text="Loading services..." />}>
            {services.map((service) => (
              <ServiceCard
                key={service.id}
                service={service}
              />
            ))}
          </Suspense>
        </div>
      </div>
    </div>
  );
};

export default ServicesPage;
