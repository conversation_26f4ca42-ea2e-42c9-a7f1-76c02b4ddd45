import { describe, it, expect, vi, beforeEach } from 'vitest';
import { pricingCalculator } from '../pricing';
import type { PropertySize, CleaningFrequency, PricingConfiguration } from '../../types';

// Mock the pricing hook and services
vi.mock('../hooks/usePricing', () => ({
  getGlobalPricingConfig: vi.fn()
}));

vi.mock('../services', () => ({
  getDynamicHourlyServices: vi.fn()
}));

describe('Pricing Calculator', () => {
  describe('Fixed Pricing Services', () => {
    it('should calculate basic house cleaning price', () => {
      const result = pricingCalculator.calculate(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0,
        150
      );

      expect(result).toEqual({
        subtotal: 150,
        discountAmount: 0,
        total: 150,
        isHourly: false
      });
    });

    it('should apply property size multipliers correctly', () => {
      const testCases = [
        { size: 'small' as PropertySize, expected: 150 },
        { size: 'medium' as PropertySize, expected: 225 },
        { size: 'large' as PropertySize, expected: 300 },
        { size: 'xlarge' as PropertySize, expected: 450 }
      ];

      testCases.forEach(({ size, expected }) => {
        const result = pricingCalculator.calculate(
          'house',
          size,
          'one-time' as CleaningFrequency,
          [],
          0,
          150
        );
        expect(result.subtotal).toBe(expected);
      });
    });

    it('should apply frequency discounts correctly', () => {
      const testCases = [
        { frequency: 'one-time' as CleaningFrequency, expected: 150 },
        { frequency: 'weekly' as CleaningFrequency, expected: 128 }, // 15% discount
        { frequency: 'bi-weekly' as CleaningFrequency, expected: 135 }, // 10% discount
        { frequency: 'monthly' as CleaningFrequency, expected: 143 } // 5% discount
      ];

      testCases.forEach(({ frequency, expected }) => {
        const result = pricingCalculator.calculate(
          'house',
          'small' as PropertySize,
          frequency,
          [],
          0,
          150
        );
        expect(result.subtotal).toBe(expected);
      });
    });

    it('should add additional services cost', () => {
      const result = pricingCalculator.calculate(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        ['Deep cleaning', 'Carpet cleaning'],
        0,
        150
      );

      expect(result.subtotal).toBe(200); // 150 + (2 * 25)
    });

    it('should apply discount percentage', () => {
      const result = pricingCalculator.calculate(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        10, // 10% discount
        150
      );

      expect(result.discountAmount).toBe(15);
      expect(result.total).toBe(135);
    });

    it('should handle complex calculation with all factors', () => {
      const result = pricingCalculator.calculate(
        'office',
        'large' as PropertySize, // 2x multiplier
        'weekly' as CleaningFrequency, // 0.85 multiplier
        ['Deep cleaning', 'Window cleaning'], // +50
        15, // 15% discount
        200
      );

      // Base: 200 * 2 * 0.85 = 340
      // Additional: 2 * 25 = 50
      // Subtotal: 340 + 50 = 390
      // Discount: 390 * 0.15 = 58.5 (rounded to 59)
      // Total: 390 - 59 = 331
      expect(result.subtotal).toBe(390);
      expect(result.discountAmount).toBe(59);
      expect(result.total).toBe(331);
    });
  });

  describe('Hourly Pricing Services', () => {
    it('should calculate customer supply hourly service', () => {
      const result = pricingCalculator.calculate(
        'hourly-customer-supply',
        'small' as PropertySize, // Not used for hourly
        'one-time' as CleaningFrequency,
        [],
        0,
        25, // Base price (hourly rate)
        3 // Selected hours
      );

      expect(result).toEqual({
        subtotal: 75, // 25 * 3
        discountAmount: 0,
        total: 75,
        isHourly: true,
        hourlyRate: 25,
        estimatedHours: 3
      });
    });

    it('should calculate company supply hourly service', () => {
      const result = pricingCalculator.calculate(
        'hourly-company-supply',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0,
        35,
        4
      );

      expect(result.subtotal).toBe(140); // 35 * 4
      expect(result.hourlyRate).toBe(35);
      expect(result.estimatedHours).toBe(4);
    });

    it('should calculate backyard hourly service', () => {
      const result = pricingCalculator.calculate(
        'backyard-hourly',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0,
        30,
        5
      );

      expect(result.subtotal).toBe(150); // 30 * 5
      expect(result.hourlyRate).toBe(30);
      expect(result.estimatedHours).toBe(5);
    });

    it('should use minimum hours when selectedHours is not provided', () => {
      const result = pricingCalculator.calculate(
        'hourly-customer-supply',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0,
        25
        // No selectedHours provided
      );

      expect(result.estimatedHours).toBe(2); // Minimum hours
      expect(result.subtotal).toBe(50); // 25 * 2
    });

    it('should apply frequency discounts to hourly rates', () => {
      const result = pricingCalculator.calculate(
        'hourly-customer-supply',
        'small' as PropertySize,
        'weekly' as CleaningFrequency, // 15% discount
        [],
        0,
        25,
        3
      );

      expect(result.hourlyRate).toBe(21.25); // 25 * 0.85
      expect(result.subtotal).toBe(64); // Math.round(21.25 * 3)
    });

    it('should add additional services with reduced rate for hourly', () => {
      const result = pricingCalculator.calculate(
        'hourly-customer-supply',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        ['Deep cleaning', 'Window cleaning'],
        0,
        25,
        3
      );

      expect(result.subtotal).toBe(105); // (25 * 3) + (2 * 15)
    });

    it('should apply discount to hourly service total', () => {
      const result = pricingCalculator.calculate(
        'hourly-customer-supply',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        20, // 20% discount
        25,
        4
      );

      expect(result.subtotal).toBe(100);
      expect(result.discountAmount).toBe(20);
      expect(result.total).toBe(80);
    });

    it('should handle complex hourly calculation', () => {
      const result = pricingCalculator.calculate(
        'hourly-company-supply',
        'medium' as PropertySize, // Not used for hourly
        'bi-weekly' as CleaningFrequency, // 10% discount
        ['Deep cleaning'],
        10, // 10% discount code
        35,
        6
      );

      // Hourly rate: 35 * 0.9 = 31.5
      // Base cost: 31.5 * 6 = 189
      // Additional: 1 * 15 = 15
      // Subtotal: 189 + 15 = 204
      // Discount: 204 * 0.1 = 20.4 (rounded to 20)
      // Total: 204 - 20 = 184
      expect(result.hourlyRate).toBe(31.5);
      expect(result.subtotal).toBe(204);
      expect(result.discountAmount).toBe(20);
      expect(result.total).toBe(184);
    });
  });

  describe('Edge Cases', () => {
    it('should handle zero additional services', () => {
      const result = pricingCalculator.calculate(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0,
        150
      );

      expect(result.subtotal).toBe(150);
    });

    it('should handle zero discount', () => {
      const result = pricingCalculator.calculate(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0,
        150
      );

      expect(result.discountAmount).toBe(0);
      expect(result.total).toBe(150);
    });

    it('should round monetary values correctly', () => {
      const result = pricingCalculator.calculate(
        'house',
        'medium' as PropertySize, // 1.5 multiplier
        'weekly' as CleaningFrequency, // 0.85 multiplier
        [],
        0,
        100
      );

      // 100 * 1.5 * 0.85 = 127.5, should round to 128
      expect(result.subtotal).toBe(128);
    });

    it('should handle large discount percentages', () => {
      const result = pricingCalculator.calculate(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        50, // 50% discount
        200
      );

      expect(result.discountAmount).toBe(100);
      expect(result.total).toBe(100);
    });
  });

  describe('Dynamic Pricing', () => {
    const mockPricingConfig: PricingConfiguration = {
      services: [
        { id: 'house', name: 'House Cleaning', basePrice: 180, isHourly: false },
        { id: 'office', name: 'Office Cleaning', basePrice: 250, isHourly: false },
        { id: 'hourly-customer-supply', name: 'Hourly (Customer Supply)', basePrice: 30, isHourly: true, rate: 30, minHours: 2 }
      ],
      multipliers: {
        sizeMultipliers: { small: 1, medium: 1.6, large: 2.2, xlarge: 3.5 },
        frequencyDiscounts: { 'one-time': 1, weekly: 0.8, 'bi-weekly': 0.85, monthly: 0.9 },
        estimatedHours: { small: 2.5, medium: 3.5, large: 4.5, xlarge: 6.5 }
      },
      lastUpdated: '2024-01-01T00:00:00Z',
      updatedBy: '<EMAIL>'
    };

    beforeEach(() => {
      const { getGlobalPricingConfig } = require('../hooks/usePricing');
      getGlobalPricingConfig.mockResolvedValue(mockPricingConfig);
    });

    it('should calculate dynamic pricing for fixed services', async () => {
      const result = await pricingCalculator.calculateDynamic(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0
      );

      expect(result).toEqual({
        subtotal: 180,
        discountAmount: 0,
        total: 180,
        isHourly: false
      });
    });

    it('should apply dynamic size multipliers', async () => {
      const result = await pricingCalculator.calculateDynamic(
        'house',
        'medium' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0
      );

      expect(result.subtotal).toBe(288); // 180 * 1.6
      expect(result.total).toBe(288);
    });

    it('should apply dynamic frequency discounts', async () => {
      const result = await pricingCalculator.calculateDynamic(
        'house',
        'small' as PropertySize,
        'weekly' as CleaningFrequency,
        [],
        0
      );

      expect(result.subtotal).toBe(144); // 180 * 0.8
      expect(result.total).toBe(144);
    });

    it('should calculate dynamic hourly pricing', async () => {
      // Mock dynamic hourly services
      const { getDynamicHourlyServices } = require('../services');
      vi.mocked(getDynamicHourlyServices).mockResolvedValue({
        'hourly-customer-supply': {
          rate: 30,
          minHours: 2,
          supplyMethod: 'customer'
        }
      });

      const result = await pricingCalculator.calculateDynamic(
        'hourly-customer-supply',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0,
        3 // 3 hours
      );

      expect(result.isHourly).toBe(true);
      expect(result.hourlyRate).toBe(30);
      expect(result.estimatedHours).toBe(3);
      expect(result.subtotal).toBe(90); // 30 * 3
    });

    it('should fallback to static pricing on error', async () => {
      const { getGlobalPricingConfig } = require('../hooks/usePricing');
      getGlobalPricingConfig.mockRejectedValue(new Error('Firebase error'));

      const result = await pricingCalculator.calculateDynamic(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0
      );

      // Should fallback to static calculation
      expect(result.isHourly).toBe(false);
      expect(result.total).toBeGreaterThan(0);
    });
  });
});
