import React, { useState, useEffect } from 'react';
import { DollarSign, Save, RefreshCw, AlertCircle, CheckCircle, Settings } from 'lucide-react';
import { firebaseService } from '../services/firebase';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from './LoadingSpinner';
import Toast from './Toast';
import type { PricingConfiguration, ServicePricing, PricingMultipliers } from '../types';

interface PricingManagementProps {
  onUpdate?: () => void;
}

const PricingManagement: React.FC<PricingManagementProps> = ({ onUpdate }) => {
  const { user } = useAuth();
  const [pricingConfig, setPricingConfig] = useState<PricingConfiguration | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<'services' | 'multipliers'>('services');
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null);

  useEffect(() => {
    loadPricingConfiguration();
  }, []);

  const loadPricingConfiguration = async () => {
    setLoading(true);
    try {
      const config = await firebaseService.admin.getPricingConfiguration();
      if (config) {
        setPricingConfig(config);
      } else {
        // Initialize with default configuration
        const defaultConfig: Omit<PricingConfiguration, 'lastUpdated' | 'updatedBy'> = {
          services: [
            { id: 'house', name: 'House Cleaning', basePrice: 150, isHourly: false },
            { id: 'office', name: 'Office Cleaning', basePrice: 200, isHourly: false },
            { id: 'hotel', name: 'Hotel Cleaning', basePrice: 300, isHourly: false },
            { id: 'hourly-customer-supply', name: 'Hourly (Customer Supply)', basePrice: 25, isHourly: true, rate: 25, minHours: 2 },
            { id: 'hourly-company-supply', name: 'Hourly (Company Supply)', basePrice: 35, isHourly: true, rate: 35, minHours: 2 },
            { id: 'backyard-hourly', name: 'Backyard Cleaning (Hourly)', basePrice: 30, isHourly: true, rate: 30, minHours: 2 }
          ],
          multipliers: {
            sizeMultipliers: { small: 1, medium: 1.5, large: 2, xlarge: 3 },
            frequencyDiscounts: { 'one-time': 1, weekly: 0.85, 'bi-weekly': 0.9, monthly: 0.95 },
            estimatedHours: { small: 2, medium: 3, large: 4, xlarge: 6 }
          }
        };
        await saveConfiguration(defaultConfig);
      }
    } catch (error) {
      console.error('Error loading pricing configuration:', error);
      setToast({ message: 'Failed to load pricing configuration', type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const saveConfiguration = async (config: Omit<PricingConfiguration, 'lastUpdated' | 'updatedBy'>) => {
    if (!user?.email) return;
    
    setSaving(true);
    try {
      await firebaseService.admin.savePricingConfiguration(config, user.email);
      await loadPricingConfiguration(); // Reload to get updated timestamps
      setToast({ message: 'Pricing configuration saved successfully', type: 'success' });
      onUpdate?.();
    } catch (error) {
      console.error('Error saving pricing configuration:', error);
      setToast({ message: 'Failed to save pricing configuration', type: 'error' });
    } finally {
      setSaving(false);
    }
  };

  const updateServicePrice = (serviceId: string, field: keyof ServicePricing, value: number | string) => {
    if (!pricingConfig) return;

    const updatedServices = pricingConfig.services.map(service =>
      service.id === serviceId ? { ...service, [field]: value } : service
    );

    setPricingConfig({
      ...pricingConfig,
      services: updatedServices
    });
  };

  const updateMultiplier = (category: keyof PricingMultipliers, key: string, value: number) => {
    if (!pricingConfig) return;

    setPricingConfig({
      ...pricingConfig,
      multipliers: {
        ...pricingConfig.multipliers,
        [category]: {
          ...pricingConfig.multipliers[category],
          [key]: value
        }
      }
    });
  };

  const handleSave = () => {
    if (!pricingConfig) return;
    
    const { lastUpdated, updatedBy, ...configToSave } = pricingConfig;
    saveConfiguration(configToSave);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  if (!pricingConfig) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load Pricing Configuration</h3>
        <p className="text-gray-600 mb-4">There was an error loading the pricing configuration.</p>
        <button
          onClick={loadPricingConfiguration}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Settings className="h-8 w-8 text-green-600 mr-3" />
            Pricing Management
          </h2>
          <p className="text-gray-600 mt-1">Configure service prices and pricing multipliers</p>
        </div>
        <button
          onClick={handleSave}
          disabled={saving}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
        >
          {saving ? (
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {saving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>

      {/* Last Updated Info */}
      {pricingConfig.lastUpdated && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-blue-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-blue-900">
                Last updated: {new Date(pricingConfig.lastUpdated).toLocaleString()}
              </p>
              <p className="text-sm text-blue-700">
                Updated by: {pricingConfig.updatedBy}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('services')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'services'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Service Prices
          </button>
          <button
            onClick={() => setActiveTab('multipliers')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'multipliers'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Pricing Multipliers
          </button>
        </nav>
      </div>

      {/* Service Prices Tab */}
      {activeTab === 'services' && (
        <div className="space-y-6">
          <div className="grid gap-6">
            {pricingConfig.services.map((service) => (
              <div key={service.id} className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{service.name}</h3>
                    <p className="text-sm text-gray-500">Service ID: {service.id}</p>
                  </div>
                  <div className="flex items-center">
                    <DollarSign className="h-5 w-5 text-green-600" />
                    <span className="text-lg font-bold text-green-600 ml-1">
                      {service.isHourly ? `${service.rate}/hr` : service.basePrice}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Base Price ($)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={service.basePrice}
                      onChange={(e) => updateServicePrice(service.id, 'basePrice', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    />
                  </div>

                  {service.isHourly && (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Hourly Rate ($)
                        </label>
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          value={service.rate || 0}
                          onChange={(e) => updateServicePrice(service.id, 'rate', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Minimum Hours
                        </label>
                        <input
                          type="number"
                          min="1"
                          step="1"
                          value={service.minHours || 1}
                          onChange={(e) => updateServicePrice(service.id, 'minHours', parseInt(e.target.value) || 1)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Pricing Multipliers Tab */}
      {activeTab === 'multipliers' && (
        <div className="space-y-6">
          {/* Size Multipliers */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Property Size Multipliers</h3>
            <p className="text-sm text-gray-600 mb-4">
              These multipliers are applied to base prices based on property size.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(pricingConfig.multipliers.sizeMultipliers).map(([size, multiplier]) => (
                <div key={size}>
                  <label className="block text-sm font-medium text-gray-700 mb-2 capitalize">
                    {size === 'xlarge' ? 'Extra Large' : size}
                  </label>
                  <input
                    type="number"
                    min="0.1"
                    step="0.1"
                    value={multiplier}
                    onChange={(e) => updateMultiplier('sizeMultipliers', size, parseFloat(e.target.value) || 1)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Frequency Discounts */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Frequency Discounts</h3>
            <p className="text-sm text-gray-600 mb-4">
              These multipliers are applied based on cleaning frequency. Values less than 1.0 represent discounts.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(pricingConfig.multipliers.frequencyDiscounts).map(([frequency, discount]) => (
                <div key={frequency}>
                  <label className="block text-sm font-medium text-gray-700 mb-2 capitalize">
                    {frequency.replace('-', ' ')}
                  </label>
                  <input
                    type="number"
                    min="0.1"
                    max="1.5"
                    step="0.05"
                    value={discount}
                    onChange={(e) => updateMultiplier('frequencyDiscounts', frequency, parseFloat(e.target.value) || 1)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {discount < 1 ? `${Math.round((1 - discount) * 100)}% discount` : 'No discount'}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Estimated Hours */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Estimated Hours by Property Size</h3>
            <p className="text-sm text-gray-600 mb-4">
              Estimated hours for hourly services based on property size.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(pricingConfig.multipliers.estimatedHours).map(([size, hours]) => (
                <div key={size}>
                  <label className="block text-sm font-medium text-gray-700 mb-2 capitalize">
                    {size === 'xlarge' ? 'Extra Large' : size}
                  </label>
                  <input
                    type="number"
                    min="1"
                    step="0.5"
                    value={hours}
                    onChange={(e) => updateMultiplier('estimatedHours', size, parseFloat(e.target.value) || 1)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">{hours} hours</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Toast */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export default PricingManagement;
