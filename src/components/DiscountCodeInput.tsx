import React, { useState, memo } from 'react';
import { Tag, Loader, AlertCircle } from 'lucide-react';
import type { DiscountCode } from '../types';

interface DiscountCodeInputProps {
  onApply: (code: string) => Promise<void>;
  appliedDiscount: DiscountCode | null;
  isLoading: boolean;
  onClear: () => void;
  error?: string | null;
}

const DiscountCodeInput: React.FC<DiscountCodeInputProps> = ({ 
  onApply, 
  appliedDiscount, 
  isLoading, 
  onClear,
  error: externalError 
}) => {
  const [code, setCode] = useState('');

  const handleSubmit = async () => {
    if (!code.trim()) return;
    
    await onApply(code);
    setCode('');
  };

  return (
    <div className="bg-gray-50 rounded-lg p-4" data-testid="discount-code-input">
      <h3 className="font-medium text-gray-900 mb-3">Discount Code</h3>

      <div className="space-y-3">
        <div className="flex gap-2">
          <input
            type="text"
            value={code}
            onChange={(e) => setCode(e.target.value.toUpperCase())}
            placeholder="Enter discount code"
            data-testid="discount-code"
            className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            disabled={isLoading}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleSubmit();
              }
            }}
          />
          <button
            type="button"
            onClick={handleSubmit}
            disabled={isLoading || !code.trim()}
            className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? <Loader className="h-4 w-4 animate-spin" /> : 'Apply'}
          </button>
        </div>
      </div>

      {externalError && (
        <div className="flex items-center mt-2 text-sm text-red-600">
          <AlertCircle className="h-4 w-4 mr-1" />
          {externalError}
        </div>
      )}

      {appliedDiscount && (
        <div className="flex items-center justify-between mt-2 p-2 bg-green-50 rounded-lg">
          <div className="flex items-center text-sm text-green-700">
            <Tag className="h-4 w-4 mr-1" />
            {appliedDiscount.percentage}% discount applied
          </div>
          <button
            onClick={onClear}
            className="text-xs text-green-600 hover:text-green-800"
          >
            Remove
          </button>
        </div>
      )}
    </div>
  );
};

DiscountCodeInput.displayName = 'DiscountCodeInput';

export default memo(DiscountCodeInput);
